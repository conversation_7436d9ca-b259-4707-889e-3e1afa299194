package com.example.habits9.ui.habitreorder

import com.example.habits9.data.Habit
import com.example.habits9.data.HabitRepository
import com.example.habits9.data.HabitSortType
import com.example.habits9.data.UserPreferencesRepository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*

@OptIn(ExperimentalCoroutinesApi::class)
class HabitReorderViewModelTest {

    private lateinit var habitRepository: HabitRepository
    private lateinit var userPreferencesRepository: UserPreferencesRepository
    private lateinit var viewModel: HabitReorderViewModel
    
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        habitRepository = mockk(relaxed = true)
        userPreferencesRepository = mockk(relaxed = true)
        
        // Mock repository responses
        coEvery { habitRepository.getAllHabits() } returns flowOf(createTestHabits())
        coEvery { habitRepository.updateCustomOrderIndices(any()) } returns true
        coEvery { userPreferencesRepository.updateHabitSortType(any()) } returns Unit
        coEvery { userPreferencesRepository.updateCustomHabitOrder(any()) } returns Unit
        
        viewModel = HabitReorderViewModel(habitRepository, userPreferencesRepository)
    }

    @Test
    fun `moveHabit should reorder habits correctly`() = runTest {
        // Given
        val habits = createTestHabits()

        // When
        viewModel.moveHabit(0, 2) // Move first habit to third position

        // Then
        val reorderedHabits = viewModel.uiState.value.habits
        assertEquals("Habit 2", reorderedHabits[0].name)
        assertEquals("Habit 3", reorderedHabits[1].name)
        assertEquals("Habit 1", reorderedHabits[2].name)
    }

    @Test
    fun `moveHabit should handle downward movement correctly`() = runTest {
        // Given: [Hab1, Hab2, Hab3, Hab4] (indices 0,1,2,3)
        val habits = listOf(
            Habit(id = 1, name = "Hab1"),
            Habit(id = 2, name = "Hab2"),
            Habit(id = 3, name = "Hab3"),
            Habit(id = 4, name = "Hab4")
        )
        coEvery { habitRepository.getAllHabits() } returns flowOf(habits)

        // When: Move Hab1 (index 0) to position 3 (after Hab3)
        viewModel.moveHabit(0, 3)

        // Then: Should be [Hab2, Hab3, Hab1, Hab4]
        val reorderedHabits = viewModel.uiState.value.habits
        assertEquals("Hab2", reorderedHabits[0].name)
        assertEquals("Hab3", reorderedHabits[1].name)
        assertEquals("Hab1", reorderedHabits[2].name)
        assertEquals("Hab4", reorderedHabits[3].name)
    }

    @Test
    fun `moveHabit should handle upward movement correctly`() = runTest {
        // Given: [Hab1, Hab2, Hab3, Hab4] (indices 0,1,2,3)
        val habits = listOf(
            Habit(id = 1, name = "Hab1"),
            Habit(id = 2, name = "Hab2"),
            Habit(id = 3, name = "Hab3"),
            Habit(id = 4, name = "Hab4")
        )
        coEvery { habitRepository.getAllHabits() } returns flowOf(habits)

        // When: Move Hab4 (index 3) to position 1 (after Hab1)
        viewModel.moveHabit(3, 1)

        // Then: Should be [Hab1, Hab4, Hab2, Hab3]
        val reorderedHabits = viewModel.uiState.value.habits
        assertEquals("Hab1", reorderedHabits[0].name)
        assertEquals("Hab4", reorderedHabits[1].name)
        assertEquals("Hab2", reorderedHabits[2].name)
        assertEquals("Hab3", reorderedHabits[3].name)
    }

    @Test
    fun `saveOrder should call batch update with correct indices`() = runTest {
        // Given
        val habits = createTestHabits()
        viewModel.moveHabit(0, 2) // Reorder habits
        
        // When
        viewModel.saveOrder()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Then
        coVerify { 
            habitRepository.updateCustomOrderIndices(
                match { orderMap ->
                    orderMap.size == 3 &&
                    orderMap.values.containsAll(listOf(0, 1, 2))
                }
            )
        }
        coVerify { userPreferencesRepository.updateCustomHabitOrder(any()) }
    }

    @Test
    fun `saveOrder should fallback to individual updates if batch fails`() = runTest {
        // Given
        coEvery { habitRepository.updateCustomOrderIndices(any()) } returns false
        viewModel.moveHabit(0, 1)
        
        // When
        viewModel.saveOrder()
        testDispatcher.scheduler.advanceUntilIdle()
        
        // Then
        coVerify { habitRepository.updateCustomOrderIndices(any()) }
        coVerify(atLeast = 1) { habitRepository.updateHabit(any()) }
    }

    @Test
    fun `saveOrder should clear reordered state after successful persistence`() = runTest {
        // Given
        coEvery { habitRepository.updateCustomOrderIndices(any()) } returns true
        viewModel.moveHabit(0, 2)

        // When
        viewModel.saveOrder()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then - reordered state should be cleared after successful save
        val reorderedHabits = viewModel.uiState.value.habits
        // Should fall back to repository data (original order) since reordered state is cleared
        assertEquals("Habit 1", reorderedHabits[0].name)
    }

    @Test
    fun `saveOrderWithoutClearing should not clear reordered state`() = runTest {
        // Given
        coEvery { habitRepository.updateCustomOrderIndices(any()) } returns true
        viewModel.moveHabit(0, 2)

        // When
        viewModel.saveOrderWithoutClearing()
        testDispatcher.scheduler.advanceUntilIdle()

        // Then - reordered state should remain
        val reorderedHabits = viewModel.uiState.value.habits
        assertEquals("Habit 2", reorderedHabits[0].name) // Still showing reordered state
        assertEquals("Habit 3", reorderedHabits[1].name)
        assertEquals("Habit 1", reorderedHabits[2].name)
    }

    @Test
    fun `clearReorderedState should reset reordered habits`() = runTest {
        // Given
        viewModel.moveHabit(0, 2)

        // When
        viewModel.clearReorderedState()

        // Then
        val habits = viewModel.uiState.value.habits
        assertEquals("Habit 1", habits[0].name) // Back to original order
    }

    private fun createTestHabits(): List<Habit> {
        return listOf(
            Habit(
                id = 1L,
                name = "Habit 1",
                uuid = "uuid1",
                position = 0,
                customOrderIndex = -1
            ),
            Habit(
                id = 2L,
                name = "Habit 2",
                uuid = "uuid2",
                position = 1,
                customOrderIndex = -1
            ),
            Habit(
                id = 3L,
                name = "Habit 3",
                uuid = "uuid3",
                position = 2,
                customOrderIndex = -1
            )
        )
    }
}
