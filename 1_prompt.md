# Prompt: Finalize Core Drag-and-Drop Functionality

## 1. Objective & Context

The objective is to fix several critical bugs in the "Reorder Habits" screen's drag-and-drop implementation. While the initial "lift" animation is working, the core functionality is broken in three ways:
1.  **Items Disappear:** When one habit is being dragged, all other habits in the list incorrectly disappear, leaving only the dragged item visible.
2.  **Drop Fails:** When the user releases the dragged habit, it snaps back to its original position instead of staying in the new one.
3.  **No Displacement Animation:** The other items in the list do not move out of the way to create a visual gap for the dragged item.

This prompt provides a step-by-step plan to fix all three issues and deliver the fully functional, polished reordering experience we intended.

## 2. Detailed Implementation Plan

We will address these bugs in order of severity, from broken functionality to visual polish.

### Part 1: Fix Disappearing Items and Failing Drop (The Core Logic)

These two bugs are likely linked and represent a fundamental flaw in how the list's data is being managed during and after the drag operation.

**Task 2.1: Fix the Disappearing Items**
- **Root Cause:** The other items disappearing is a strong indication that the `RecyclerView`'s adapter is being incorrectly told that the list is empty or that its drawing is being improperly intercepted.
- **Solution:**
    1.  Review the drag-and-drop callback implementation. If there are custom implementations of the drawing methods (e.g., `onDraw` or `onDrawOver`), ensure they call their `super` method. If they are not needed for custom drawing, remove them entirely to let the system handle the default drawing behavior.
    2.  Verify that the underlying data list that feeds the adapter is not being accidentally cleared or modified at the start of a drag gesture.

**Task 2.2: Fix the "Snap Back" / Failing Drop**
- **Root Cause:** The item snapping back means the change to the item's position is never being committed to the local data list that the adapter uses. The UI visually reverts to the last known "true" state of that list.
- **Solution:**
    1.  In the drag-and-drop callback, locate the method responsible for handling the move event (often named `onMove`). **This is the most critical step.** Inside this method, you must update the order of the items in your local data list (e.g., using `Collections.swap` or remove/add). After updating the list, you must call the adapter's notification method (e.g., `notifyItemMoved(from, to)`).
    2.  Locate the method that is called when the user releases the item and the drag is complete (often named `clearView`). Inside this method, after calling the `super` implementation, you must trigger the logic to make the temporary order permanent (i.e., prepare it to be saved when the user taps "Save").

### Part 2: Implement Fluid Item Displacement Animation (The Polish)

With the core functionality fixed, we can now implement the smooth animations.

**Task 2.3: Enable the Displacement Animation**
- **Root Cause:** The `RecyclerView` animates item movements by default when its adapter's notification methods are called correctly. The lack of this animation means `notifyItemMoved` is likely not being called properly from the `onMove` method as described in Task 2.2.
- **Solution:**
    1.  Ensure the `onMove` method is correctly implemented as per Task 2.2. The call to `notifyItemMoved` is what triggers the `RecyclerView`'s `ItemAnimator`.
    2.  The `ItemAnimator` is responsible for the visual effect of other items sliding out of the way. Confirm that a default animator is set on the `RecyclerView` and has not been disabled or replaced with one that doesn't support move animations.

## 3. Verification Plan

Please follow these steps meticulously to confirm all fixes.

1.  Navigate to the "Reorder Habits" screen.
2.  Long-press a habit to start dragging it.
3.  **Verify (Fix 1):** All other habits in the list **remain visible**. They do not disappear.
4.  Drag the item to a new position in the list and release it.
5.  **Verify (Fix 2):** The item **stays** in the new position. It does not snap back to where it started.
6.  Drag another item over the other items in the list.
7.  **Verify (Fix 3):** The other items now **animate smoothly** out of the way to create a visual gap.
8.  Confirm that you can reorder the entire list, tap "Save," and the new order is correctly reflected on the main habits screen.

## 4. Mandatory Development Guidelines

- **Refer to the Style Guide:** Before starting any feature, always consult the project's style guide for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns. It is the single source of truth for styling decisions.
- **Study the Reference Project:** Prior to implementation, review the reference project to understand how similar features have been approached to maintain consistency and avoid duplications or contradictions. The reference project serves as a blueprint for implementation. This step is mandatory. Do not proceed to implementation without this step.
- **Understand the Existing Project Structure:** Before writing any code, spend time exploring and understanding how the current system is structured. Even for new features, existing components or utility functions may be reusable. Integrate changes cleanly into the existing architecture instead of creating disconnected code.
- **Maintain a Clean Codebase:** After implementing features, remove any temporary, test, or duplicate files, folders, or unused components that were created during development. Keep the codebase organized and clutter-free.
- **Pause If There Is Any Confusion:** If at any point the requirements are unclear, do not proceed based on assumptions. Immediately pause and seek clarification. It is better to get clarity than to redo or fix avoidable mistakes later.
- **Remove Unused Old Implementations:** As part of final review, identify and delete any old, unused code that was implemented earlier but is no longer in use. This includes obsolete modules, features, or legacy logic.